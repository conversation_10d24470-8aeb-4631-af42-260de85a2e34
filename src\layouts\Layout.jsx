import React, { useState } from 'react';
import { Outlet } from 'react-router-dom';
import Sidebar from './Sidebar';
import '../styles/Layout.css';

const Layout = () => {
  const [isSidebarExpanded, setIsSidebarExpanded] = useState(false);

  const handleSidebarToggle = (expanded) => {
    setIsSidebarExpanded(expanded);
  };

  return (
    <div className="flex h-screen bg-[#F8FAFC] overflow-hidden">
      <Sidebar onToggle={handleSidebarToggle} />
      <main 
        className={`
          flex-1 overflow-auto p-6
          transition-all duration-600 ease-in-out
          ${isSidebarExpanded ? 'ml-[325px]' : 'ml-[54px]'}
        `}
      >
        <div className="container mx-auto">
          <Outlet />
        </div>
      </main>
    </div>
  );
};

export default Layout;
