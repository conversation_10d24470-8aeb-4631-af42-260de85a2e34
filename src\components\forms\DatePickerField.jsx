import React from 'react';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import calendarIcon from '../../assets/icons/calendar.svg'

const DatePickerField = ({
  label,
  name,
  value,
  onChange,
  error,
  minDate,
  maxDate,
  placeholder = 'Select date...',
  className = '',
  labelClassName = '',
  required = false,
  disabled = false,
  showTimeSelect = false,
  dateFormat = 'MM/dd/yyyy',
  ...props
}) => {

  // Custom input component to include the calendar icon
  const CustomInput = React.forwardRef(({ value, onClick }, ref) => (
    <div className="relative w-full">
      <input
        value={value || ''}
        onChange={() => {}}
        onClick={onClick}
        ref={ref}
        placeholder={placeholder}
        className={`w-full px-4 py-2 rounded-lg border border-[#E8E8E8] bg-white text-base placeholder:text-[#64748B] placeholder:text-xs focus:outline-none focus:ring-2 focus:ring-blue-500 ${className}`}
      />
      <div className="absolute inset-y-0 right-0 flex items-center pr-4">
        <img 
          src={calendarIcon} 
          alt="calendar" 
          className="w-5 h-5"
          onClick={onClick}
        />
      </div>
    </div>
  ));

  return (
    <div className="form-field">
      {label && (
        <label 
          htmlFor={name} 
          className={labelClassName}
        >
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      <DatePicker
        id={name}
        name={name}
        selected={value}
        onChange={onChange}
        minDate={minDate}
        maxDate={maxDate}
        disabled={disabled}
        showTimeSelect={showTimeSelect}
        dateFormat={dateFormat}
        customInput={<CustomInput />}
        {...props}
      />
      {error && (
        <p className="mt-1 text-sm text-red-500">{error}</p>
      )}
    </div>
  );
};

// Add custom styles for the datepicker to ensure proper appearance
const styles = `
  .react-datepicker-wrapper {
    width: 100%;
  }
  .react-datepicker__input-container {
    width: 100%;
  }
`;

// Add styles to the document
const styleSheet = document.createElement("style");
styleSheet.textContent = styles;
document.head.appendChild(styleSheet);

export default DatePickerField;
