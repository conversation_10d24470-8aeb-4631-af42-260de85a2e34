import React from 'react'
import Container from '../components/container'
import FilterComponent from '../components/forms/FilterComponent'

const Dashboard = () => {
  return (
    <div className="dashboard-container flex flex-col items-center">
      <FilterComponent/>

      <h2>Dashboard</h2>

      <Container title="Site Documentation Photos">
      <p>Your content goes here</p>
      {/* Any content, components, etc. */}
      <div className="dashboard-content">
        <p>Welcome to AISIN dashboard. Use the sidebar to navigate between different sections.</p>
        Lorem ipsum dolor sit, amet consectetur adipisicing elit. Totam maiores, deleniti veritatis consectetur dolores consequatur quos distinctio in, repellat nihil ad quo asperiores rem provident et corporis nesciunt corrupti saepe accusamus culpa praesentium, odit pariatur natus cum? Iste voluptates, quo, repudiandae deleniti quia illo maxime ducimus vero, sapiente mollitia aperiam? Beatae id, quasi eligendi voluptates dolor molestias autem aliquam laboriosam pariatur reiciendis voluptatem, animi sint adipisci vero, necessitatibus aperiam corporis recusandae asperiores perferendis non sunt repellendus! Ut, recusandae eligendi aut necessitatibus sunt adipisci quae inventore qui similique quo ipsam nesciunt reiciendis, voluptates unde, nihil minus. Illum accusamus repellat deserunt ab suscipit repudiandae nisi excepturi itaque nulla eaque, minima ut nihil, sit et numquam rerum ea explicabo accusantium harum placeat, aliquid hic similique. Beatae enim eos labore laudantium quis! Aut totam nostrum libero, iste ad magnam! Voluptatum neque repellat officia, laudantium asperiores velit iure tenetur? Corporis, quis tempore asperiores impedit suscipit fugiat corrupti veniam qui cupiditate nisi debitis dolor perferendis, consequuntur saepe! Culpa obcaecati facere praesentium maiores quis ex quisquam aliquid blanditiis vel velit. Officia eveniet maxime quisquam harum omnis blanditiis vero laborum distinctio? Officia est laborum sapiente amet exercitationem consequatur velit voluptatum! Culpa quas velit deleniti consectetur dolor illum optio beatae ipsa expedita laboriosam molestiae temporibus officiis, recusandae consequatur. Ipsa, consequatur sint! Quam assumenda delectus fugit magni voluptatum quidem quisquam eaque provident recusandae expedita! Commodi quo iusto officiis, laboriosam dolorem nam nulla vel vitae sed debitis iure deserunt in consequuntur saepe obcaecati. Ut aliquam, consequuntur, magnam molestiae sequi eaque sapiente illo voluptate odio similique debitis quisquam eum laboriosam corrupti ullam explicabo suscipit rerum? Voluptas culpa sunt, sint modi distinctio ratione qui sit quasi porro iure dolorem nostrum, quas necessitatibus. Quibusdam corporis adipisci officia molestias odio esse doloribus ut ea totam eum! Dolorem sapiente repellendus nulla. Voluptatibus ex molestias eum dignissimos ad nemo dolor, quibusdam alias illum voluptatum harum repudiandae. Optio debitis dignissimos esse nulla suscipit quos recusandae facilis adipisci veritatis. Repellat, repudiandae minus perferendis, sed laboriosam autem possimus explicabo ut tempora molestiae, illum reiciendis doloribus. Nihil possimus harum sint vitae cumque labore tempore nostrum vero corporis dolor dolores hic, aperiam a quaerat deleniti aliquam. Impedit repellendus pariatur dignissimos voluptate, ullam nesciunt ipsa reiciendis, ad totam alias velit odit magni provident voluptatum consequatur recusandae rem libero doloribus animi numquam ex rerum. Officia sed sapiente iure mollitia veritatis laudantium assumenda qui minima, eos vel minus voluptatibus vero beatae asperiores atque porro? Reprehenderit tenetur libero asperiores consectetur nobis, eius labore commodi blanditiis ad error accusamus corrupti tempora doloribus sapiente. Debitis fugit dolores nam, voluptate mollitia repudiandae deserunt cupiditate quidem blanditiis doloribus nobis deleniti sunt, tempore saepe laborum quisquam voluptatibus! At sapiente maxime aliquid, facere consequatur magnam dolorum soluta quas! Vero dicta blanditiis voluptatum consequuntur suscipit velit minus minima facilis qui perspiciatis porro repellat, provident cumque tempora iusto libero temporibus id quae reprehenderit obcaecati repellendus possimus atque. Cumque doloribus ipsum amet et qui, dicta dolores, in officiis quos consequuntur odio adipisci, excepturi ut nihil aspernatur dolorem optio! Magni vitae iste soluta temporibus, fuga cumque?
      </div>
    </Container>

    </div>
  )
}

export default Dashboard